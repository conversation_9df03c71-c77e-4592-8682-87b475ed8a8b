import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardHeader } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Label } from '../ui/label';
import { Badge } from '../ui/Badge';
import { Alert, AlertDescription } from '../ui/alert';
import { 
  ArrowUpDown, 
  Settings, 
  Info,
  Loader2,
  AlertTriangle,
  TrendingUp,
  TrendingDown
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useWeb3 } from '../../providers/Web3Provider';
import { useEnhancedBalances, useBalances } from '../../hooks/useContracts';
import { useSwap, useDEXFormatting } from '../../hooks/useDEX';
import { getExchangeRateCorrection } from '../../lib/portfolioCorrection';
import { SwapParams, DEFAULT_SLIPPAGE_CONFIG } from '../../lib/dexUtils';
import { SlippageSettings, useSlippageSettings } from './SlippageSettings';
import { DEXErrorDisplay, DEXLoadingState } from './DEXErrorDisplay';

interface SwapCardProps {
  onSwapComplete?: () => void;
}

export function SwapCard({ onSwapComplete }: SwapCardProps) {
  const { account, isConnected, isCorrectNetwork } = useWeb3();
  const { formattedBalances, loading: balancesLoading, correctionApplied } = useEnhancedBalances();
  const { balances: rawBalances } = useBalances(); // For calculations and validation
  const { swapQuote, loading, error, getQuote, executeSwap, clearQuote, clearError } = useSwap();
  const { formatTokenAmount, parseTokenAmount, formatPercentage, formatPriceImpact } = useDEXFormatting();
  const { slippage, setSlippage, isHighSlippage, isVeryHighSlippage } = useSlippageSettings(0.5);

  // Calculate corrected wallet balances for DEX trading
  const getCorrectedWalletBalance = (rawBalance: bigint, tokenType: 'share' | 'usdt'): string => {
    if (tokenType === 'usdt') {
      // USDT doesn't need correction
      return formatTokenAmount(rawBalance, 6, 2);
    }

    // For SHARE tokens, apply correction if needed
    if (correctionApplied && rawBalance > 0n) {
      const correctionFactor = getExchangeRateCorrection(rawBalance);
      const correctedBalance = BigInt(Math.floor(Number(rawBalance) * correctionFactor));
      return formatTokenAmount(correctedBalance, 18, 4);
    }

    return formatTokenAmount(rawBalance, 18, 4);
  };

  // Convert user input (corrected amount) back to raw amount for smart contract
  const convertToRawAmount = (userInput: string, tokenType: 'share' | 'usdt'): bigint => {
    if (tokenType === 'usdt') {
      // USDT doesn't need conversion
      return parseTokenAmount(userInput, 6);
    }

    // For SHARE tokens, convert corrected input back to raw amount
    if (correctionApplied && rawBalances.share > 0n) {
      const correctionFactor = getExchangeRateCorrection(rawBalances.share);
      if (correctionFactor < 1) {
        // Convert user input back to raw amount
        const userAmount = parseTokenAmount(userInput, 18);
        const rawAmount = BigInt(Math.floor(Number(userAmount) / correctionFactor));
        return rawAmount;
      }
    }

    return parseTokenAmount(userInput, 18);
  };

  // Form state
  const [tokenIn, setTokenIn] = useState<'share' | 'usdt'>('share');
  const [amountIn, setAmountIn] = useState('');
  const [isAdvancedMode, setIsAdvancedMode] = useState(false);
  const [isExecuting, setIsExecuting] = useState(false);

  // Auto-refresh quote when inputs change
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (amountIn && !isNaN(parseFloat(amountIn)) && parseFloat(amountIn) > 0) {
        try {
          // Convert user input (corrected amount) to raw amount for smart contract
          const amount = convertToRawAmount(amountIn, tokenIn);
          getQuote(tokenIn, amount, { tolerance: slippage, deadline: 300 });
        } catch (error) {
          console.error('Error getting quote:', error);
        }
      } else {
        clearQuote();
      }
    }, 500); // Debounce for 500ms

    return () => clearTimeout(timeoutId);
  }, [amountIn, tokenIn, slippage, getQuote, clearQuote, convertToRawAmount]);

  // Handle token swap (flip input/output)
  const handleTokenSwap = useCallback(() => {
    setTokenIn(tokenIn === 'share' ? 'usdt' : 'share');
    setAmountIn('');
    clearQuote();
  }, [tokenIn, clearQuote]);

  // Handle amount input change
  const handleAmountChange = useCallback((value: string) => {
    setAmountIn(value);
  }, []);

  // Execute swap transaction
  const handleSwap = useCallback(async () => {
    if (!account || !swapQuote) {
      toast.error('Please connect wallet and get a quote first');
      return;
    }

    setIsExecuting(true);

    try {
      const deadline = Math.floor(Date.now() / 1000) + (5 * 60); // 5 minutes from now

      const params: SwapParams = {
        tokenIn,
        amountIn: swapQuote.amountIn,
        minimumAmountOut: swapQuote.minimumAmountOut,
        deadline,
        userAddress: account,
      };

      const tx = await executeSwap(params);
      
      toast.success('Swap completed successfully!');
      
      // Clear form
      setAmountIn('');
      clearQuote();
      
      // Notify parent component
      if (onSwapComplete) {
        onSwapComplete();
      }

      return tx;
    } catch (error: any) {
      console.error('Failed to execute swap:', error);
      toast.error(error.message || 'Failed to execute swap');
    } finally {
      setIsExecuting(false);
    }
  }, [account, swapQuote, tokenIn, executeSwap, clearQuote, onSwapComplete]);

  // Validation
  const isValidInput = amountIn && !isNaN(parseFloat(amountIn)) && parseFloat(amountIn) > 0;

  const hasInsufficientBalance = isValidInput && (
    tokenIn === 'share'
      ? convertToRawAmount(amountIn, 'share') > rawBalances.share
      : parseTokenAmount(amountIn, 6) > rawBalances.usdt
  );

  const canExecute = isConnected && isCorrectNetwork && isValidInput && 
    !hasInsufficientBalance && swapQuote && !loading && !isExecuting;

  // Get token info with corrected wallet balances
  const tokenInInfo = {
    symbol: tokenIn === 'share' ? 'BLOCKS' : 'USDT',
    balance: tokenIn === 'share' ? rawBalances.share : rawBalances.usdt,
    formattedBalance: tokenIn === 'share'
      ? getCorrectedWalletBalance(rawBalances.share, 'share')
      : getCorrectedWalletBalance(rawBalances.usdt, 'usdt'),
    decimals: tokenIn === 'share' ? 18 : 6,
  };

  const tokenOutInfo = {
    symbol: tokenIn === 'share' ? 'USDT' : 'BLOCKS',
    balance: tokenIn === 'share' ? rawBalances.usdt : rawBalances.share,
    formattedBalance: tokenIn === 'share'
      ? getCorrectedWalletBalance(rawBalances.usdt, 'usdt')
      : getCorrectedWalletBalance(rawBalances.share, 'share'),
    decimals: tokenIn === 'share' ? 6 : 18,
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <ArrowUpDown className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-semibold">Swap Tokens</h3>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsAdvancedMode(!isAdvancedMode)}
          >
            <Settings className="h-4 w-4" />
          </Button>
        </div>
        <p className="text-sm text-gray-600">
          Trade BLOCKS and USDT instantly
        </p>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Token Input */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <Label htmlFor="amountIn">From</Label>
            <div className="flex items-center gap-2">
              <span className="text-xs text-gray-500">
                Balance: {tokenInInfo.formattedBalance}
              </span>
              {correctionApplied && tokenIn === 'share' && rawBalances.share > 0n && getExchangeRateCorrection(rawBalances.share) < 1 && (
                <Badge variant="secondary" className="text-xs">
                  Corrected
                </Badge>
              )}
            </div>
          </div>
          <div className="relative">
            <Input
              id="amountIn"
              type="number"
              placeholder="0.0"
              value={amountIn}
              onChange={(e) => handleAmountChange(e.target.value)}
              className="pr-20"
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <Badge variant="secondary" className="text-xs">{tokenInInfo.symbol}</Badge>
            </div>
          </div>
          <div className="flex justify-end">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                // Use the corrected formatted balance for user-facing input
                setAmountIn(tokenInInfo.formattedBalance);
              }}
              className="text-xs h-6 px-2"
            >
              MAX
            </Button>
          </div>
        </div>

        {/* Swap Button */}
        <div className="flex justify-center">
          <Button
            variant="outline"
            size="sm"
            onClick={handleTokenSwap}
            className="rounded-full p-2"
          >
            <ArrowUpDown className="h-4 w-4" />
          </Button>
        </div>

        {/* Token Output */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <Label>To</Label>
            <div className="flex items-center gap-2">
              <span className="text-xs text-gray-500">
                Balance: {tokenOutInfo.formattedBalance}
              </span>
              {correctionApplied && tokenIn === 'usdt' && rawBalances.share > 0n && getExchangeRateCorrection(rawBalances.share) < 1 && (
                <Badge variant="secondary" className="text-xs">
                  Corrected
                </Badge>
              )}
            </div>
          </div>
          <div className="relative">
            <Input
              type="text"
              placeholder="0.0"
              value={swapQuote ? formatTokenAmount(swapQuote.amountOut, tokenOutInfo.decimals, tokenOutInfo.decimals === 6 ? 2 : 4) : ''}
              readOnly
              className="pr-20 bg-gray-50"
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <Badge variant="secondary" className="text-xs">{tokenOutInfo.symbol}</Badge>
            </div>
          </div>
        </div>

        {/* Advanced Settings */}
        {isAdvancedMode && (
          <div className="p-3 bg-gray-50 rounded-lg">
            <SlippageSettings
              slippage={slippage}
              onSlippageChange={setSlippage}
            />
          </div>
        )}

        {/* Quote Display */}
        {swapQuote && (
          <div className="space-y-2 p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center gap-2">
              <Info className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">Swap Details</span>
            </div>
            <div className="space-y-1 text-xs text-blue-700">
              <div className="flex justify-between">
                <span>Minimum Received:</span>
                <span>{formatTokenAmount(swapQuote.minimumAmountOut, tokenOutInfo.decimals, tokenOutInfo.decimals === 6 ? 2 : 4)} {tokenOutInfo.symbol}</span>
              </div>
              <div className="flex justify-between">
                <span>Price Impact:</span>
                <span className={formatPriceImpact(swapQuote.priceImpact).color}>
                  {formatPriceImpact(swapQuote.priceImpact).value}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Slippage Tolerance:</span>
                <span>{formatPercentage(slippage)}</span>
              </div>
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <DEXErrorDisplay
            error={error}
            onRetry={() => {
              clearError();
              if (amountIn && tokenIn) {
                const amount = convertToRawAmount(amountIn, tokenIn);
                getQuote(tokenIn, amount, { tolerance: slippage, deadline: 300 });
              }
            }}
            onDismiss={clearError}
          />
        )}

        {/* High Slippage Warning */}
        {isVeryHighSlippage && (
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-700">
              Very high slippage tolerance! You may lose significant value to MEV attacks.
            </AlertDescription>
          </Alert>
        )}

        {/* Insufficient Balance Warning */}
        {hasInsufficientBalance && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Insufficient {tokenInInfo.symbol} balance. Please reduce the amount or add more tokens to your wallet.
            </AlertDescription>
          </Alert>
        )}

        {/* Swap Button */}
        <Button
          onClick={handleSwap}
          disabled={!canExecute}
          className="w-full"
          size="lg"
        >
          {isExecuting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Swapping...
            </>
          ) : loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Getting Quote...
            </>
          ) : !isConnected ? (
            'Connect Wallet'
          ) : !isCorrectNetwork ? (
            'Switch to BSC Testnet'
          ) : !isValidInput ? (
            'Enter Amount'
          ) : hasInsufficientBalance ? (
            'Insufficient Balance'
          ) : !swapQuote ? (
            'Get Quote'
          ) : (
            `Swap ${tokenInInfo.symbol} for ${tokenOutInfo.symbol}`
          )}
        </Button>
      </CardContent>
    </Card>
  );
}
